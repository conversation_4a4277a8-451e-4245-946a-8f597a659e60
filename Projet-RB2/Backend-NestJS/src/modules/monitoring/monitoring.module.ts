import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { PrismaModule } from '../../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';

// Controllers
import { MetricsController } from './controllers/metrics.controller';
import { AlertsController } from './controllers/alerts.controller';

// Services
import { PrometheusService } from './services/prometheus.service';
import { AlertService } from './services/alert.service';
import { RecommendationMonitoringService } from './services/recommendation-monitoring.service';

// Configuration
import monitoringConfig from '../../config/monitoring.config';

/**
 * Module de monitoring
 */
@Module({
  imports: [
    PrismaModule,
    HttpModule,
    ScheduleModule.forRoot(),
    ConfigModule.forFeature(monitoringConfig),
    AuthModule,
  ],
  controllers: [
    MetricsController,
    AlertsController,
  ],
  providers: [
    PrometheusService,
    AlertService,
    RecommendationMonitoringService,
  ],
  exports: [
    PrometheusService,
    AlertService,
    RecommendationMonitoringService,
  ],
})
export class MonitoringModule {}
