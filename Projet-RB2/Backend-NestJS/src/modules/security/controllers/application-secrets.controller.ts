import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../../common/decorators/roles.decorator';
import { ApplicationSecretsService, ApplicationSecret } from '../services/application-secrets.service';

@ApiTags('application-secrets')
@Controller('api/security/application-secrets')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class ApplicationSecretsController {
  private readonly logger = new Logger(ApplicationSecretsController.name);

  constructor(private readonly applicationSecretsService: ApplicationSecretsService) {}

  @Post()
  @Roles('admin')
  @ApiOperation({ summary: 'Crée un nouveau secret d\'application' })
  @ApiResponse({ status: 201, description: 'Secret créé avec succès' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['name', 'value', 'application'],
      properties: {
        name: { type: 'string', example: 'database-password' },
        value: { type: 'string', example: 'secret-value' },
        application: { type: 'string', example: 'user-service' },
        environment: { type: 'string', example: 'production' },
        description: { type: 'string', example: 'Database password for user service' },
        owner: { type: 'string', example: 'team-backend' },
        expiresInDays: { type: 'number', example: 90 },
        rotationInterval: { type: 'number', example: 2592000000 }, // 30 jours en ms
        autoRotate: { type: 'boolean', example: true },
      },
    },
  })
  async createSecret(
    @Body() body: {
      name: string;
      value: string;
      application: string;
      environment?: string;
      description?: string;
      owner?: string;
      expiresInDays?: number;
      rotationInterval?: number;
      autoRotate?: boolean;
    },
  ) {
    try {
      const secretId = await this.applicationSecretsService.createSecret(
        body.name,
        body.value,
        body.application,
        {
          environment: body.environment,
          description: body.description,
          owner: body.owner,
          expiresInDays: body.expiresInDays,
          rotationInterval: body.rotationInterval,
          autoRotate: body.autoRotate,
        },
      );

      return {
        statusCode: HttpStatus.CREATED,
        message: 'Application secret created successfully',
        data: { secretId },
      };
    } catch (error) {
      this.logger.error(`Failed to create application secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to create application secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('applications')
  @Roles('admin')
  @ApiOperation({ summary: 'Liste toutes les applications qui ont des secrets' })
  @ApiResponse({ status: 200, description: 'Liste des applications récupérée avec succès' })
  async listApplications() {
    try {
      const applications = await this.applicationSecretsService.listApplications();

      return {
        statusCode: HttpStatus.OK,
        data: { applications },
      };
    } catch (error) {
      this.logger.error(`Failed to list applications: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to list applications',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':application')
  @Roles('admin')
  @ApiOperation({ summary: 'Liste tous les secrets d\'une application' })
  @ApiResponse({ status: 200, description: 'Liste des secrets récupérée avec succès' })
  @ApiParam({ name: 'application', description: 'Nom de l\'application' })
  @ApiQuery({ name: 'environment', required: false, description: 'Environnement' })
  async listSecrets(
    @Param('application') application: string,
    @Query('environment') environment?: string,
  ) {
    try {
      const secrets = await this.applicationSecretsService.listSecrets(application, environment);

      // Filtrer les informations sensibles
      const filteredSecrets = secrets.map(secret => this.filterSensitiveInfo(secret));

      return {
        statusCode: HttpStatus.OK,
        data: { secrets: filteredSecrets },
      };
    } catch (error) {
      this.logger.error(`Failed to list application secrets: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to list application secrets',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':application/:name')
  @Roles('admin')
  @ApiOperation({ summary: 'Récupère un secret d\'application' })
  @ApiResponse({ status: 200, description: 'Secret récupéré avec succès' })
  @ApiParam({ name: 'application', description: 'Nom de l\'application' })
  @ApiParam({ name: 'name', description: 'Nom du secret' })
  @ApiQuery({ name: 'environment', required: false, description: 'Environnement' })
  async getSecret(
    @Param('application') application: string,
    @Param('name') name: string,
    @Query('environment') environment?: string,
  ) {
    try {
      const secret = await this.applicationSecretsService.getSecret(name, application, environment);

      if (!secret) {
        throw new HttpException(
          {
            statusCode: HttpStatus.NOT_FOUND,
            message: 'Secret not found',
          },
          HttpStatus.NOT_FOUND,
        );
      }

      return {
        statusCode: HttpStatus.OK,
        data: {
          name: secret.name,
          value: secret.value,
          metadata: this.filterSensitiveInfo(secret).metadata,
        },
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`Failed to get application secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to get application secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':application/:name')
  @Roles('admin')
  @ApiOperation({ summary: 'Met à jour un secret d\'application' })
  @ApiResponse({ status: 200, description: 'Secret mis à jour avec succès' })
  @ApiParam({ name: 'application', description: 'Nom de l\'application' })
  @ApiParam({ name: 'name', description: 'Nom du secret' })
  @ApiQuery({ name: 'environment', required: false, description: 'Environnement' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['value'],
      properties: {
        value: { type: 'string', example: 'new-secret-value' },
      },
    },
  })
  async updateSecret(
    @Param('application') application: string,
    @Param('name') name: string,
    @Query('environment') environment?: string,
    @Body() body: { value: string },
  ) {
    try {
      const secretId = await this.applicationSecretsService.updateSecret(
        name,
        body.value,
        application,
        environment,
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Application secret updated successfully',
        data: { secretId },
      };
    } catch (error) {
      this.logger.error(`Failed to update application secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to update application secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':application/:name/rotate')
  @Roles('admin')
  @ApiOperation({ summary: 'Effectue la rotation d\'un secret d\'application' })
  @ApiResponse({ status: 200, description: 'Rotation du secret effectuée avec succès' })
  @ApiParam({ name: 'application', description: 'Nom de l\'application' })
  @ApiParam({ name: 'name', description: 'Nom du secret' })
  @ApiQuery({ name: 'environment', required: false, description: 'Environnement' })
  async rotateSecret(
    @Param('application') application: string,
    @Param('name') name: string,
    @Query('environment') environment?: string,
  ) {
    try {
      const newSecretId = await this.applicationSecretsService.rotateSecret(
        name,
        application,
        environment,
      );

      return {
        statusCode: HttpStatus.OK,
        message: 'Application secret rotated successfully',
        data: { newSecretId },
      };
    } catch (error) {
      this.logger.error(`Failed to rotate application secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to rotate application secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':application/:name')
  @Roles('admin')
  @ApiOperation({ summary: 'Supprime un secret d\'application' })
  @ApiResponse({ status: 200, description: 'Secret supprimé avec succès' })
  @ApiParam({ name: 'application', description: 'Nom de l\'application' })
  @ApiParam({ name: 'name', description: 'Nom du secret' })
  @ApiQuery({ name: 'environment', required: false, description: 'Environnement' })
  async deleteSecret(
    @Param('application') application: string,
    @Param('name') name: string,
    @Query('environment') environment?: string,
  ) {
    try {
      await this.applicationSecretsService.deleteSecret(name, application, environment);

      return {
        statusCode: HttpStatus.OK,
        message: 'Application secret deleted successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to delete application secret: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Failed to delete application secret',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Filtre les informations sensibles d'un secret d'application
   * @param secret Secret d'application
   * @returns Secret filtré
   */
  private filterSensitiveInfo(secret: ApplicationSecret): Omit<ApplicationSecret, 'value'> & { value?: string } {
    const { name, metadata } = secret;
    return {
      name,
      metadata,
    };
  }
}
