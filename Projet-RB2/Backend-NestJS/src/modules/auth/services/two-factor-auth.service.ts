import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../../prisma/prisma.service';
import { authenticator } from 'otplib';
import { toDataURL } from 'qrcode';
import * as crypto from 'crypto';

/**
 * Service pour gérer l'authentification à deux facteurs (2FA)
 * Utilise TOTP (Time-based One-Time Password) selon la norme RFC 6238
 */
@Injectable()
export class TwoFactorAuthService {
  private readonly logger = new Logger(TwoFactorAuthService.name);
  private readonly appName: string;

  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {
    this.appName = this.configService.get<string>('APP_NAME', 'Retreat And Be');
    
    // Configurer la bibliothèque TOTP
    authenticator.options = {
      window: this.configService.get<number>('TWO_FACTOR_WINDOW', 1), // Nombre de périodes de 30 secondes
      digits: 6,
      algorithm: 'sha1',
      step: 30, // Période de 30 secondes
    };
  }

  /**
   * Générer un secret 2FA pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param email Email de l'utilisateur
   */
  async generateTwoFactorSecret(userId: string, email: string): Promise<{ secret: string; otpAuthUrl: string; qrCodeDataUrl: string }> {
    try {
      // Générer un secret aléatoire
      const secret = authenticator.generateSecret();
      
      // Créer l'URL d'authentification TOTP
      const otpAuthUrl = authenticator.keyuri(email, this.appName, secret);
      
      // Générer le QR code
      const qrCodeDataUrl = await toDataURL(otpAuthUrl);
      
      // Stocker le secret temporairement (non activé)
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorSecret: secret,
          twoFactorEnabled: false,
        },
      });
      
      return {
        secret,
        otpAuthUrl,
        qrCodeDataUrl,
      };
    } catch (error) {
      this.logger.error(`Failed to generate 2FA secret for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Activer l'authentification à deux facteurs pour un utilisateur
   * @param userId ID de l'utilisateur
   * @param token Token TOTP fourni par l'utilisateur
   */
  async enableTwoFactor(userId: string, token: string): Promise<boolean> {
    try {
      // Récupérer l'utilisateur et son secret 2FA
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          twoFactorSecret: true,
          twoFactorEnabled: true,
        },
      });
      
      if (!user || !user.twoFactorSecret) {
        throw new UnauthorizedException('Two-factor authentication not initialized');
      }
      
      if (user.twoFactorEnabled) {
        throw new UnauthorizedException('Two-factor authentication already enabled');
      }
      
      // Vérifier le token
      const isValid = authenticator.verify({
        token,
        secret: user.twoFactorSecret,
      });
      
      if (!isValid) {
        throw new UnauthorizedException('Invalid two-factor token');
      }
      
      // Activer 2FA pour l'utilisateur
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorEnabled: true,
          // Générer des codes de récupération
          twoFactorRecoveryCodes: this.generateRecoveryCodes(),
        },
      });
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to enable 2FA for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Désactiver l'authentification à deux facteurs pour un utilisateur
   * @param userId ID de l'utilisateur
   */
  async disableTwoFactor(userId: string): Promise<boolean> {
    try {
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorSecret: null,
          twoFactorEnabled: false,
          twoFactorRecoveryCodes: null,
        },
      });
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to disable 2FA for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Vérifier un token 2FA
   * @param userId ID de l'utilisateur
   * @param token Token TOTP ou code de récupération
   */
  async verifyTwoFactorToken(userId: string, token: string): Promise<boolean> {
    try {
      // Récupérer l'utilisateur et son secret 2FA
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          twoFactorSecret: true,
          twoFactorEnabled: true,
          twoFactorRecoveryCodes: true,
        },
      });
      
      if (!user || !user.twoFactorEnabled || !user.twoFactorSecret) {
        throw new UnauthorizedException('Two-factor authentication not enabled');
      }
      
      // Vérifier si c'est un code de récupération
      if (user.twoFactorRecoveryCodes && this.verifyRecoveryCode(userId, token, user.twoFactorRecoveryCodes)) {
        return true;
      }
      
      // Vérifier le token TOTP
      const isValid = authenticator.verify({
        token,
        secret: user.twoFactorSecret,
      });
      
      if (!isValid) {
        throw new UnauthorizedException('Invalid two-factor token');
      }
      
      return true;
    } catch (error) {
      this.logger.error(`Failed to verify 2FA token for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Générer de nouveaux codes de récupération pour un utilisateur
   * @param userId ID de l'utilisateur
   */
  async generateNewRecoveryCodes(userId: string): Promise<string[]> {
    try {
      const recoveryCodes = this.generateRecoveryCodes();
      
      await this.prisma.user.update({
        where: { id: userId },
        data: {
          twoFactorRecoveryCodes: recoveryCodes,
        },
      });
      
      return recoveryCodes;
    } catch (error) {
      this.logger.error(`Failed to generate new recovery codes for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Générer des codes de récupération aléatoires
   * @returns Tableau de codes de récupération
   */
  private generateRecoveryCodes(count = 10): string[] {
    const codes: string[] = [];
    
    for (let i = 0; i < count; i++) {
      // Générer un code de 10 caractères (5 groupes de 4 caractères)
      const code = crypto.randomBytes(15)
        .toString('hex')
        .toUpperCase()
        .replace(/(.{4})/g, '$1-')
        .slice(0, 24); // 5 groupes de 4 caractères + 4 tirets = 24 caractères
      
      codes.push(code);
    }
    
    return codes;
  }

  /**
   * Vérifier un code de récupération
   * @param userId ID de l'utilisateur
   * @param code Code de récupération à vérifier
   * @param recoveryCodes Liste des codes de récupération de l'utilisateur
   */
  private async verifyRecoveryCode(userId: string, code: string, recoveryCodes: string[]): Promise<boolean> {
    // Normaliser le code (supprimer les tirets, espaces, etc.)
    const normalizedCode = code.replace(/[^A-Z0-9]/g, '').toUpperCase();
    
    // Vérifier si le code existe dans la liste
    const matchIndex = recoveryCodes.findIndex(rc => rc.replace(/[^A-Z0-9]/g, '').toUpperCase() === normalizedCode);
    
    if (matchIndex === -1) {
      return false;
    }
    
    // Supprimer le code utilisé
    const updatedCodes = [...recoveryCodes];
    updatedCodes.splice(matchIndex, 1);
    
    // Mettre à jour la liste des codes
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        twoFactorRecoveryCodes: updatedCodes,
      },
    });
    
    return true;
  }
}
